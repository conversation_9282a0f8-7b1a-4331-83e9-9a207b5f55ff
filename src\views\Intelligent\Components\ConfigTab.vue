<script setup lang="ts">
  import { reactive, ref, nextTick, watch } from 'vue';
  import {
    EditOutlined,
    LoadingOutlined,
    PlusOutlined,
    DeleteOutlined,
    MinusOutlined,
    InfoCircleOutlined,
    FileTextOutlined,
  } from '@ant-design/icons-vue';
  import Accordion from '../../Knowledge/Components/Accordion.vue';
  import { message } from 'ant-design-vue';
  import { uploadImages } from '@/api/exploration';
  import { example, templateStr } from '..';
  import AddKnowledge from './addKnowledge.vue';
  import type { IKnowledgeFilterItem } from '@/interface/knowledge';
  import ModelConfig from './modelConfig.vue';
  import type { ICallParams } from '@/interface/exploration';
  import Preview from '../preview.vue';
  import type { Rule } from 'ant-design-vue/es/form';

  // Props 定义
  interface Props {
    formState: any;
    roleInstruction: any;
    openerPrompt: any;
    suggestedQuestions: any;
    backgroundImage: any;
    memoryConfig: any;
    knowledgeDb: any;
    knowledgeDbList: IKnowledgeFilterItem[];
    generationConfig: any;
    deployId: string;
    upStatus: any;
    knowledgeUpStatue: any;
    dialogueUpStatue: any;
    memoryUpStatue: any;
    customMemoryConfig: any[];
    state: any;
    reloadKey: number;
    agentName: string;
    columns: any[];
    memoryRegex: RegExp;
  }

  const props = defineProps<Props>();

  // Emits 定义
  const emit = defineEmits<{
    'update:formState': [value: any];
    'update:roleInstruction': [value: any];
    'update:openerPrompt': [value: any];
    'update:suggestedQuestions': [value: any];
    'update:backgroundImage': [value: any];
    'update:memoryConfig': [value: any];
    'update:knowledgeDb': [value: any];
    'update:knowledgeDbList': [value: IKnowledgeFilterItem[]];
    'update:generationConfig': [value: any];
    'update:deployId': [value: string];
    'update:customMemoryConfig': [value: any[]];
    'update:state': [value: any];
    'save': [key?: string];
    'changeGenerationConfig': [value: ICallParams, deployId: string];
    'checkAgentName': [name: string];
  }>();

  // Refs
  const iconFormRef = ref();
  const nameFormRef = ref();
  const addKnowledgeRef = ref();

  // 本地响应式数据
  const localFormState = reactive({ ...props.formState });
  const localRoleInstruction = reactive({ ...props.roleInstruction });
  const localOpenerPrompt = reactive({ ...props.openerPrompt });
  const localSuggestedQuestions = reactive({ ...props.suggestedQuestions });
  const localBackgroundImage = reactive({ ...props.backgroundImage });
  const localMemoryConfig = reactive({ ...props.memoryConfig });
  const localKnowledgeDb = reactive({ ...props.knowledgeDb });
  const localGenerationConfig = reactive({ ...props.generationConfig });
  const localCustomMemoryConfig = reactive([...props.customMemoryConfig]);
  const localState = reactive({ ...props.state });

  // 监听 props 变化并同步到本地数据
  watch(() => props.formState, (newVal) => Object.assign(localFormState, newVal), { deep: true });
  watch(() => props.roleInstruction, (newVal) => Object.assign(localRoleInstruction, newVal), { deep: true });
  watch(() => props.openerPrompt, (newVal) => Object.assign(localOpenerPrompt, newVal), { deep: true });
  watch(() => props.suggestedQuestions, (newVal) => Object.assign(localSuggestedQuestions, newVal), { deep: true });
  watch(() => props.backgroundImage, (newVal) => Object.assign(localBackgroundImage, newVal), { deep: true });
  watch(() => props.memoryConfig, (newVal) => Object.assign(localMemoryConfig, newVal), { deep: true });
  watch(() => props.knowledgeDb, (newVal) => Object.assign(localKnowledgeDb, newVal), { deep: true });
  watch(() => props.generationConfig, (newVal) => Object.assign(localGenerationConfig, newVal), { deep: true });
  watch(() => props.customMemoryConfig, (newVal) => Object.assign(localCustomMemoryConfig, newVal), { deep: true });
  watch(() => props.state, (newVal) => Object.assign(localState, newVal), { deep: true });

  // 监听本地数据变化并向父组件发送更新
  watch(localFormState, (newVal) => emit('update:formState', newVal), { deep: true });
  watch(localRoleInstruction, (newVal) => emit('update:roleInstruction', newVal), { deep: true });
  watch(localOpenerPrompt, (newVal) => emit('update:openerPrompt', newVal), { deep: true });
  watch(localSuggestedQuestions, (newVal) => emit('update:suggestedQuestions', newVal), { deep: true });
  watch(localBackgroundImage, (newVal) => emit('update:backgroundImage', newVal), { deep: true });
  watch(localMemoryConfig, (newVal) => emit('update:memoryConfig', newVal), { deep: true });
  watch(localKnowledgeDb, (newVal) => emit('update:knowledgeDb', newVal), { deep: true });
  watch(localGenerationConfig, (newVal) => emit('update:generationConfig', newVal), { deep: true });
  watch(localCustomMemoryConfig, (newVal) => emit('update:customMemoryConfig', newVal), { deep: true });
  watch(localState, (newVal) => emit('update:state', newVal), { deep: true });

  // 方法定义
  const closeUpStatus = (target: string, resource: Record<string, boolean>) => {
    const temp = { ...resource };
    if (!temp[target]) {
      Object.keys(temp).forEach((key) => {
        temp[key] = false;
      });
      temp[target] = true;
    } else {
      temp[target] = false;
    }
    Object.assign(resource, temp);
  };

  const debouncedSave = (key?: string) => {
    emit('save', key);
  };

  const handleRemove = (record: IKnowledgeFilterItem) => {
    const index = props.knowledgeDbList.findIndex((item) => item === record);
    const newList = [...props.knowledgeDbList];
    newList.splice(index, 1);
    emit('update:knowledgeDbList', newList);
    
    const newKnowledgeDb = [...localKnowledgeDb.value];
    newKnowledgeDb.splice(index, 1);
    localKnowledgeDb.value = newKnowledgeDb;
  };

  const uploadProps = {
    beforeUpload: (file: any) => {
      const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/jpg';
      if (!isJpgOrPng) {
        message.warn('上传的图片格式不支持！');
        return false;
      }
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        message.error('上传的图片太大！');
        return false;
      }
      return isJpgOrPng && isLt10M;
    },
    customRequest: async (detail: { file: File }) => {
      const file = detail.file;
      const formData = new FormData();
      formData.append('files', file);
      localState.updateLoading = true;
      const res: { url: string }[] = await uploadImages(formData);
      localFormState.icon_url = res[0].url;
      debouncedSave('icon_url');
      localState.updateLoading = false;
    },
    multiple: false,
    fileList: [],
    accept: 'image/png,image/jpg,image/jpeg',
    showUploadList: false,
  };

  const uploadBackgroundProps = {
    beforeUpload: (file: any) => {
      const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/jpg';
      if (!isJpgOrPng) {
        message.warn('上传的图片格式不支持！');
        return false;
      }
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        message.error('上传的图片太大！');
        return false;
      }
      return isJpgOrPng && isLt10M;
    },
    customRequest: async (detail: { file: File }) => {
      const file = detail.file;
      const formData = new FormData();
      formData.append('files', file);
      localState.backgroundLoading = true;
      const res: { url: string }[] = await uploadImages(formData);
      localBackgroundImage.value = res[0].url;
      localBackgroundImage.is_enable = true;
      localState.backgroundLoading = false;
    },
    multiple: false,
    fileList: [],
    accept: 'image/png,image/jpg,image/jpeg',
    showUploadList: false,
  };

  const validatorName = async (_rule: Rule, value: string) => {
    if (value == '' || value.trim().length == 0) {
      return Promise.reject('请输入应用名称');
    }
    if (value.length > 20) {
      return Promise.reject('应用名称最多输入 20 个字');
    }
    if (value !== props.agentName) {
      try {
        await emit('checkAgentName', value);
      } catch (e) {
        if (e === 'AlreadyExists') {
          return Promise.reject('该名称已存在，请重新命名');
        }
        return Promise.reject(e);
      }
    }
    return Promise.resolve();
  };

  const rules = {
    name: [
      { required: true, validator: validatorName },
    ],
    logo: [{ required: true, message: '请选择应用图标' }],
  };

  const handleCancel = () => {
    const newList = addKnowledgeRef.value.selected_knowledge_db;
    emit('update:knowledgeDbList', newList);
    localKnowledgeDb.value = newList.map((item: IKnowledgeFilterItem) => item.id);
    localKnowledgeDb.is_enable = Boolean(newList.length);
  };

  const openerPromptChange = (e: { target: { value: string } }) => {
    localOpenerPrompt.value = e.target.value;
    localOpenerPrompt.is_enable = Boolean(e.target.value);
  };

  const suggestedQuestionsChange = (index: number) => {
    const value = localSuggestedQuestions.value[index];
    if (localSuggestedQuestions.value.length < 3 && value.trim() && index === localSuggestedQuestions.value.length - 1) {
      localSuggestedQuestions.value.push('');
    }
    localSuggestedQuestions.is_enable = Boolean(localSuggestedQuestions.value.length);
  };

  const handleChangeGenerationConfig = (value: ICallParams, deploy_id: string) => {
    Object.assign(localGenerationConfig.value, { ...value });
    emit('update:deployId', deploy_id);
    emit('changeGenerationConfig', value, deploy_id);
  };
</script>

<template>
  <div class="container">
    <a-row style="height: 100%">
      <a-col :span="12" style="height: 100%">
        <div class="left">
          <div class="left-config flex justify-between items-center">
            <div class="title-text text-16px">应用配置</div>
            <ModelConfig
              :deploy-id="deployId"
              :generation-config="localGenerationConfig.value"
              @change="handleChangeGenerationConfig"
            />
          </div>
          <div class="left-content overflow-scroll">
            <Accordion
              :expend="upStatus.basic"
              :expand-click="() => closeUpStatus('basic', upStatus)"
              :bordered="false"
            >
              <template #title>
                <div class="title-text">基本信息</div>
              </template>
              <template #content>
                <a-row>
                  <a-col :span="6">
                    <a-form ref="iconFormRef" autocomplete="off" layout="vertical" :model="localFormState">
                      <a-form-item label="应用图标" name="icon_url" :rules="rules.logo">
                        <a-upload class="avatar-uploader" v-bind="uploadProps" list-type="picture-card">
                          <div v-if="localFormState.icon_url" class="img-container">
                            <div class="img-content" :style="{ backgroundImage: `url(${localFormState.icon_url})` }" />
                            <div class="overlay">
                              <div class="edit-icon">
                                <EditOutlined style="color: #ddd; font-size: 24px" />
                              </div>
                            </div>
                          </div>
                          <div v-else>
                            <loading-outlined v-if="localState.updateLoading"></loading-outlined>
                            <plus-outlined v-else></plus-outlined>
                            <div class="ant-upload-text">上传图标</div>
                          </div>
                        </a-upload>
                      </a-form-item>
                    </a-form>
                  </a-col>
                  <a-col :span="18">
                    <a-form ref="nameFormRef" autocomplete="off" layout="vertical" :model="localFormState">
                      <a-form-item label="应用名称" name="name" :rules="rules.name">
                        <a-input
                          v-model:value="localFormState.name"
                          :maxlength="20"
                          placeholder="请输入应用名称"
                          show-count
                          @change="debouncedSave('name')"
                        ></a-input>
                      </a-form-item>
                    </a-form>
                  </a-col>
                </a-row>
                <a-form-item label="应用简介" name="introduction">
                  <a-textarea
                    ref="textareaRef"
                    v-model:value="localFormState.introduction"
                    allow-clear
                    show-count
                    :maxlength="500"
                    :auto-size="{ minRows: 6, maxRows: 6 }"
                    placeholder="请输入应用简介"
                    @change="debouncedSave('introduction')"
                  />
                </a-form-item>
              </template>
            </Accordion>
            <Accordion :expend="upStatus.instruction" :expand-click="() => closeUpStatus('instruction', upStatus)">
              <template #title>
                <div class="flex justify-between items-center w-100%">
                  <div class="title-text">
                    角色指令
                    <a-tooltip>
                      <template #title>
                        通过角色指令功能，你能够精确设定智能体应用的作用范围。包括指定应用将扮演的角色和输出结果的格式与风格。此外，你还可以规定应用不得执行哪些操作等。
                      </template>
                      <InfoCircleOutlined />
                    </a-tooltip>
                  </div>
                  <div @click.stop>
                    <a-popover title="示例" trigger="hover">
                      <template #content>
                        <div class="template-display overflow-scroll">
                          {{ example }}
                        </div>
                      </template>
                      <a>示例</a>
                    </a-popover>
                    <a-popover title="模版内容" trigger="hover">
                      <template #content>
                        <div class="template-display overflow-scroll">
                          {{ templateStr }}
                        </div>
                      </template>
                      <a class="ml-10px" @click.stop="localRoleInstruction.value = templateStr"
                        ><FileTextOutlined />使用模板</a
                      >
                    </a-popover>
                  </div>
                </div>
              </template>
              <template #content>
                <a-textarea
                  v-model:value="localRoleInstruction.value"
                  allow-clear
                  :auto-size="{ minRows: 8, maxRows: 8 }"
                  placeholder="编写系统提示词，包括角色设定、任务目标、具备的能力及回复的要求与限制等，好的提示词会直接影响智能体效果。"
                />
              </template>
            </Accordion>

            <Accordion :expend="upStatus.knowledge" :expand-click="() => closeUpStatus('knowledge', upStatus)">
              <template #title>
                <div class="title-text">知识</div>
              </template>
              <template #content>
                <Accordion
                  :expend="knowledgeUpStatue.knowledgebase"
                  :expand-click="() => closeUpStatus('knowledgebase', knowledgeUpStatue)"
                >
                  <template #title>
                    <div class="flex justify-between items-center w-100%">
                      <div class="title-text">知识库</div>
                      <div @click.stop>
                        <a-switch
                          v-model:checked="localKnowledgeDb.is_enable"
                          @change="message.success(localKnowledgeDb.is_enable ? '已开启知识库' : '已关闭知识库')"
                        />

                        <a-tooltip>
                          <template #title> 添加知识库 </template>
                          <PlusOutlined
                            style="margin-left: 10px; color: #1890ff"
                            @click.stop="localState.knowledgeVisible = true"
                          />
                        </a-tooltip>
                      </div>
                    </div>
                  </template>
                  <template #content>
                    <div v-if="knowledgeDbList && knowledgeDbList.length" class="knowledge-box overflow-scroll">
                      <div v-for="item in knowledgeDbList" :key="item.id" class="flex justify-between knowledge-item">
                        <div class="flex items-center">
                          <div>{{ item.name }}</div>
                          <a-popover>
                            <template #content>
                              <p class="title-text">
                                {{
                                  item.files.filter((item) => item.status === 'available').length
                                    ? `${item.name} 可用文件`
                                    : '暂无可用文件'
                                }}
                              </p>
                              <div
                                v-for="file in item.files.filter((item) => item.status === 'available')"
                                :key="file.id"
                                class="mb-5px"
                              >
                                {{ file.name }}
                              </div>
                            </template>
                            <a-tag color="green" style="margin: 0 5px; width: 40px"
                              >{{ item.files.filter((item) => item.status === 'available').length }}个</a-tag
                            >
                          </a-popover>
                        </div>
                        <div class="flex items-center">
                          <div class="flex items-center">
                            <template v-if="item.files.some((file) => file.status === 'available')">
                              <svg class="knowledge-icon" aria-hidden="true">
                                <use xlink:href="#icon-chenggong"></use>
                              </svg>
                            </template>
                            <template v-else>
                              <span class="text-#d9001b">此知识库无可用文件</span>
                              <svg class="knowledge-icon" aria-hidden="true">
                                <use xlink:href="#icon-warning"></use>
                              </svg>
                            </template>
                          </div>
                          <a-button danger shape="circle" size="small" @click="handleRemove(item)"
                            ><MinusOutlined
                          /></a-button>
                        </div>
                      </div>
                    </div>
                    <div v-else class="text-#797979 text-14px">
                      可上传本地文本数据、表格型知识数据构建知识库。
                      用户发送消息时，应用能够引用知识库答复。应用最多可关联50个知识库。建议详细填写知识库描述信息以提高问答准确率
                    </div>
                  </template>
                </Accordion>
              </template>
            </Accordion>
          </div>
        </div>
      </a-col>
      <a-col :span="12" style="height: 100%">
        <div class="right">
          <div class="title-text h-62px leading-62px pl-20px bg-#f5f5f5 text-16px">预览与调试</div>
          <div class="preview-box bg-#f5f5f5">
            <Preview :key="reloadKey" />
          </div>
        </div>
      </a-col>
    </a-row>
  </div>
</template>

<style scoped lang="less">
  .container {
    height: calc(100% - 40px);
    .left {
      height: 100%;
      padding: 0 10px;
      display: flex;
      flex-direction: column;
      .left-config {
        border-bottom: 1px solid #ddd;
        height: 62px;
        padding: 10px 0;
      }
      .left-content {
        height: calc(100% - 42px);
        padding: 0 5px;
        flex: 1;
      }
    }
    .right {
      height: 100%;
      padding: 0 0 10px 10px;
      .preview-box {
        height: calc(100% - 52px);
      }
    }
  }
  .img-container {
    width: 100%;
    height: 100%;
    position: relative;
    &:hover {
      .overlay {
        opacity: 1;
      }
    }
  }
  .img-content {
    width: 100%;
    height: 100%;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
  }
  .overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.4);
    display: flex;
    justify-content: space-around;
    align-items: center;
    opacity: 0;
    transition: opacity 0.3s ease;
  }
  .template-display {
    white-space: pre-wrap;
    font-family: monospace;
    background-color: #f9f9f9;
    padding: 12px;
    border-radius: 4px;
    border: 1px solid #eee;
    margin: 10px 0;
    line-height: 1.6;
    color: #222;
    height: 300px;
  }
  .knowledge-box {
    max-height: 200px;
  }
  .knowledge-item {
    border: 1px solid #ccc;
    border-radius: 6px;
    padding: 10px;
    margin-bottom: 10px;
    .knowledge-icon {
      width: 28px;
      height: 28px;
      display: flex;
      align-items: center;
      margin: 0 5px;
    }
  }
  .title-text {
    font-weight: bold;
  }
</style>
