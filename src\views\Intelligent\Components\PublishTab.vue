<script setup lang="ts">
  import { ref } from 'vue';

  // Props 定义
  interface Props {
    formState: any;
    deployId: string;
  }

  const props = defineProps<Props>();

  // Emits 定义
  const emit = defineEmits<{
    'publish': [];
  }>();

  const handlePublish = () => {
    emit('publish');
  };
</script>

<template>
  <div class="publish-tab-container">
    <div class="placeholder-content">
      <div class="publish-header">
        <h3>发布管理</h3>
        <p class="description">管理您的智能体应用发布状态和版本</p>
      </div>
      
      <div class="publish-info">
        <div class="info-card">
          <h4>当前应用信息</h4>
          <div class="info-item">
            <span class="label">应用名称：</span>
            <span class="value">{{ formState?.name || '未设置' }}</span>
          </div>
          <div class="info-item">
            <span class="label">部署ID：</span>
            <span class="value">{{ deployId || '未配置' }}</span>
          </div>
          <div class="info-item">
            <span class="label">发布状态：</span>
            <span class="value status-draft">草稿</span>
          </div>
        </div>

        <div class="info-card">
          <h4>发布说明</h4>
          <ul class="publish-notes">
            <li>发布前请确保已完成应用配置</li>
            <li>发布后用户可以访问和使用您的智能体应用</li>
            <li>您可以随时更新配置并重新发布</li>
            <li>发布功能正在开发中，敬请期待...</li>
          </ul>
        </div>
      </div>

      <div class="publish-actions">
        <a-button type="primary" size="large" @click="handlePublish" :disabled="!deployId">
          发布应用
        </a-button>
        <p class="action-tip" v-if="!deployId">
          请先配置模型后再发布
        </p>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
  .publish-tab-container {
    height: 100%;
    padding: 20px;
    background-color: #f5f5f5;
    
    .placeholder-content {
      max-width: 800px;
      margin: 0 auto;
      background: white;
      border-radius: 8px;
      padding: 30px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .publish-header {
      text-align: center;
      margin-bottom: 40px;
      
      h3 {
        font-size: 24px;
        color: #333;
        margin-bottom: 10px;
      }
      
      .description {
        color: #666;
        font-size: 16px;
        margin: 0;
      }
    }

    .publish-info {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
      margin-bottom: 40px;

      .info-card {
        background: #fafafa;
        border-radius: 6px;
        padding: 20px;
        border: 1px solid #e8e8e8;

        h4 {
          margin: 0 0 15px 0;
          color: #333;
          font-size: 16px;
          font-weight: 600;
        }

        .info-item {
          display: flex;
          margin-bottom: 10px;
          
          .label {
            color: #666;
            min-width: 80px;
          }
          
          .value {
            color: #333;
            font-weight: 500;
            
            &.status-draft {
              color: #faad14;
            }
          }
        }

        .publish-notes {
          margin: 0;
          padding-left: 20px;
          
          li {
            color: #666;
            margin-bottom: 8px;
            line-height: 1.5;
          }
        }
      }
    }

    .publish-actions {
      text-align: center;
      
      .action-tip {
        margin-top: 10px;
        color: #999;
        font-size: 14px;
      }
    }

    @media (max-width: 768px) {
      .publish-info {
        grid-template-columns: 1fr;
      }
    }
  }
</style>
