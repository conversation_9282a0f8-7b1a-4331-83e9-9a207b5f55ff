<script setup lang="ts">
  import { reactive, ref, onMounted, watch, nextTick } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import {
    LeftOutlined,
  } from '@ant-design/icons-vue';
  import dayjs from 'dayjs';
  import { message } from 'ant-design-vue';
  import { DEFAULT_CALLPARAMS } from '.';
  import type { IKnowledgeFilterItem } from '@/interface/knowledge';
  import { agentVersionDetail, checkAgentName, publishAgent, updateAgent } from '@/api/agent';
  import type { IAgentItem, IUpdateAgentProps } from '@/interface/agent';
  import type { ICallParams } from '@/interface/exploration';
  import { debounce } from '@/utils/common';
  import ConfigTab from './Components/ConfigTab.vue';
  import PublishTab from './Components/PublishTab.vue';

  interface IDefaultCloseUpDStatus {
    basic: boolean;
    instruction: boolean;
    knowledge: boolean;
    dialogue: boolean;
    memory: boolean;
  }
  const defaultCloseUpDStatus: IDefaultCloseUpDStatus = {
    basic: true,
    instruction: true,
    knowledge: true,
    dialogue: true,
    memory: true,
  };
  const upStatus = reactive({
    ...defaultCloseUpDStatus,
  });
  const knowledgeUpStatue = reactive({
    knowledgebase: true,
  });
  const dialogueUpStatue = reactive({
    prologue: true,
    suggested: true,
    background: true,
  });
  const memoryUpStatue = reactive({
    variable: true,
  });
  const columns = [
    {
      title: '字段名称',
      dataIndex: 'name',
      key: 'name',
      max: 50,
      desc: ['记忆变量的字段名称，输入要求如下：', '1.仅支持英文、数字、下划线', '2.必须以英文字母开头'],
    },
    {
      title: '字段描述',
      dataIndex: 'description',
      key: 'description',
      max: 200,
      desc: [
        '详细填写记忆变量描述，大模型将根据描述来调用并读写记忆变量，完备的记忆变量描述能够提高调用的准确性。',
        '示例：用于记录患者的姓名',
      ],
    },
    {
      title: '默认值',
      dataIndex: 'default_value',
      key: 'default_value',
      max: 500,
      desc: ['预先设定字段的默认值'],
    },
    {
      title: '记忆时长',
      dataIndex: 'duration',
      key: 'duration',
      width: 150,
      desc: [
        '用户数据记忆时长，',
        '-如选择「永久」，则该应用用户一旦赋予变量值后将会永久存储，不会随着会话关闭而恢复为默认值。',
        '-如选择「单次会话」，当一次会话结束后，则应用用户赋予的变量值会自动恢复为默认值，仅对一次会话生效。',
      ],
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      width: 100,
    },
  ];

  const router = useRouter();
  const route = useRoute();
  const updated_at = ref('');
  const agentName = ref('');
  const memoryRegex = /^[a-zA-Z][a-zA-Z0-9_]*$/;

  interface IDefault_state {
    icon_url: string;
    name: string;
    introduction: string;
    [key: string]: any;
  }
  const DEFAULT_FORM_STATE: IDefault_state = {
    icon_url: '',
    name: '',
    introduction: '',
  };
  const formState = reactive<IDefault_state>({ ...DEFAULT_FORM_STATE });
  const roleInstruction = reactive({
    value: '',
    is_enable: true,
  });
  const openerPrompt = reactive({
    value: '',
    is_enable: false,
  });
  const suggestedQuestions = reactive({
    value: [''],
    is_enable: true,
  });
  const callParams = reactive<ICallParams>({ ...DEFAULT_CALLPARAMS });
  const generationConfig = reactive({
    value: callParams,
    is_enable: true,
  });
  const deployId = ref('');
  const masterId = ref('');

  const backgroundImage = reactive({
    value: '',
    is_enable: false,
  });
  interface IMemoryValue {
    name: string;
    description: string;
    default_value: string;
    duration: string;
  }
  const memoryConfig = reactive<{ value: IMemoryValue[] | []; is_enable: boolean }>({
    value: [],
    is_enable: true,
  });
  const customMemoryConfig = reactive<IMemoryValue[]>([]);
  const knowledgeDbList = ref<IKnowledgeFilterItem[]>([]);
  const knowledgeDb = reactive<{ value: string[]; is_enable: boolean }>({
    value: [],
    is_enable: false,
  });
  const state = reactive({
    updateLoading: false,
    knowledgeVisible: false,
    backgroundLoading: false,
    memoryVisible: false,
    backgroundImageVisible: false,
  });
  const reloadKey = ref(0);
  const currentTab = ref('config');

  const handleBack = () => {
    debouncedSave();
    router.back();
  };

  const confirmSave = async (key?: string) => {
    // 过滤空白推荐问
    const params: IUpdateAgentProps = {};
    if (key) {
      // @ts-expect-error
      params[key] = formState[key];
    } else {
      const suggest = suggestedQuestions.value.filter((val) => val);
      (params.deploy_id = deployId.value),
        (params.config = {
          instruction: roleInstruction,
          knowledge_db: knowledgeDb,
          memory_config: memoryConfig,
          opener_prompt: openerPrompt,
          background_image: backgroundImage,
          suggested_questions: { value: suggest, is_enable: suggestedQuestions.is_enable },
          generation_config: generationConfig,
        });
    }
    await updateAgent(String(route.params.id), params);
    updated_at.value = dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss');
    reloadKey.value++;
  };

  const debouncedSave = debounce(confirmSave);

  const confirmPublish = async () => {
    if (!deployId.value) {
      message.warn('模型未配置，请配置后再发布');
      return;
    }
    try {
      await publishAgent(String(route.params.id));
      message.success('已提交发布');
    } catch {}
    router.back();
  };

  const handleChangeGenerationConfig = (value: ICallParams, deploy_id: string) => {
    Object.assign(generationConfig.value, { ...value });
    deployId.value = deploy_id;
  };

  const fetchAgentDetail = async () => {
    const data: IAgentItem = await agentVersionDetail(String(route.params.id));
    const { name, introduction, icon_url, config, deploy_id, master_id } = data;
    agentName.value = name;
    deployId.value = deploy_id;
    masterId.value = master_id;
    const {
      instruction,
      knowledge_db,
      memory_config,
      opener_prompt,
      background_image,
      suggested_questions,
      generation_config,
    } = config;
    Object.assign(formState, { name, introduction, icon_url });
    Object.assign(roleInstruction, { ...instruction });
    Object.assign(knowledgeDb, { ...knowledge_db, value: knowledge_db.value.map((item) => item.id) });
    knowledgeDbList.value = knowledge_db.value;
    Object.assign(memoryConfig, { ...memory_config });
    const memory_config_value = memory_config.value;
    Object.assign(customMemoryConfig, memory_config_value);
    Object.assign(openerPrompt, { ...opener_prompt });
    Object.assign(backgroundImage, { ...background_image });
    Object.assign(generationConfig, { ...generation_config });
    // 当推荐问题数量小于三个或没有时，自动补一个空白问题，方便用户输入
    const suggest =
      suggested_questions.value && suggested_questions.value.length
        ? suggested_questions.value.length < 3
          ? [...suggested_questions.value, '']
          : suggested_questions.value
        : [''];
    Object.assign(suggestedQuestions, {
      value: suggest,
      is_enable: suggested_questions.is_enable,
    });
  };

  onMounted(async () => {
    await fetchAgentDetail();
  });

  watch(
    [
      () => deployId.value,
      () => roleInstruction,
      () => knowledgeDb,
      () => openerPrompt,
      () => memoryConfig,
      () => generationConfig,
      () => suggestedQuestions,
      () => backgroundImage,
    ],
    async () => {
      await nextTick();
      await debouncedSave();
    },
    { deep: true },
  );
</script>

<template>
  <div class="header text-18px">
    <div>
      <LeftOutlined @click="handleBack" />
      <span class="m-l-10px">{{ formState.name }}</span>
    </div>
    <div class="flex text-14px text-#797979">
      <div class="leading-[32px] mr-10px">自动保存于：{{ updated_at }}</div>
      <a-radio-group v-model:value="currentTab" class="mr-10px">
        <a-radio-button value="config">配置</a-radio-button>
        <a-radio-button value="publish">发布</a-radio-button>
      </a-radio-group>
      <a-button type="primary" @click="confirmPublish">发布</a-button>
    </div>
  </div>

  <!-- 配置标签页 -->
  <ConfigTab
    v-show="currentTab === 'config'"
    :form-state="formState"
    :role-instruction="roleInstruction"
    :opener-prompt="openerPrompt"
    :suggested-questions="suggestedQuestions"
    :background-image="backgroundImage"
    :memory-config="memoryConfig"
    :knowledge-db="knowledgeDb"
    :knowledge-db-list="knowledgeDbList"
    :generation-config="generationConfig"
    :deploy-id="deployId"
    :up-status="upStatus"
    :knowledge-up-statue="knowledgeUpStatue"
    :dialogue-up-statue="dialogueUpStatue"
    :memory-up-statue="memoryUpStatue"
    :custom-memory-config="customMemoryConfig"
    :state="state"
    :reload-key="reloadKey"
    :agent-name="agentName"
    :columns="columns"
    :memory-regex="memoryRegex"
    @update:form-state="(val) => Object.assign(formState, val)"
    @update:role-instruction="(val) => Object.assign(roleInstruction, val)"
    @update:opener-prompt="(val) => Object.assign(openerPrompt, val)"
    @update:suggested-questions="(val) => Object.assign(suggestedQuestions, val)"
    @update:background-image="(val) => Object.assign(backgroundImage, val)"
    @update:memory-config="(val) => Object.assign(memoryConfig, val)"
    @update:knowledge-db="(val) => Object.assign(knowledgeDb, val)"
    @update:knowledge-db-list="(val) => knowledgeDbList = val"
    @update:generation-config="(val) => Object.assign(generationConfig, val)"
    @update:deploy-id="(val) => deployId = val"
    @update:custom-memory-config="(val) => Object.assign(customMemoryConfig, val)"
    @update:state="(val) => Object.assign(state, val)"
    @save="confirmSave"
    @change-generation-config="handleChangeGenerationConfig"
    @check-agent-name="checkAgentName"
  />

  <!-- 发布标签页 -->
  <PublishTab
    v-show="currentTab === 'publish'"
    :form-state="formState"
    :deploy-id="deployId"
    @publish="confirmPublish"
  />
</template>

<style scoped lang="less">
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 10px;
    border-bottom: 1px solid #ccc;
  }
</style>
